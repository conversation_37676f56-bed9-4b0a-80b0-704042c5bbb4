import os
import sys
import re
import json
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) # Add the project root directory to the Python path
from logdata import log_message
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch, insert_and_update_df_to_GZ_id
from AI.GC_VertexAI import vertex_genai_text, vertex_genai_text_async
from datetime import datetime
import pandas as pd
from langfuse import observe
import asyncio
import Common.Constants as Constants
from typing import Optional
from langfuse import observe
import langfuse

# ❌⚠️📥🔥✅


## Search free limits:
# - Azure / Bing: 1000 queries / month, including it has images
# - Google: 100 queries / day
# - SerpAPI: 100 queries / month
# - undetected_chromedriver: ??? queries / hour

## LLM free limits: https://github.com/cheahjs/free-llm-api-resources
# - Gemini flash: 1500 queries / day & 15 per minutes
# - Gemini pro: 50 queries / day & 2 per minutes
# - Mistral (any model): 1 request/second
# - Google Cloud: experimental models are always free, 10 queries per minute, no daily cap: https://cloud.google.com/vertex-ai/generative-ai/docs/multimodal/gemini-experimental#console


async def get_all_ai_summary_translations():
    cases_df = get_table_from_GZ("tb_case")
    await get_ai_summary_translations(cases_df)
    insert_and_update_df_to_GZ_batch(cases_df, "tb_case", "id")

@observe()
async def get_ai_summary_translations(df, indices=[], **kwargs):
    # Log input parameters
    langfuse.get_client().update_current_span(
        input={
            "total_cases": len(df),
            "target_indices": indices if indices else "all",
            "indices_count": len(indices) if indices else len(df),
            "function": "get_ai_summary_translations"
        }
    )

    log_message(f"🧠 Starting AI Summary Translation for {len(indices) if indices else len(df)} cases...", level='INFO')

    # Initialize counters and tracking
    processed_count = 0
    skipped_count = 0
    error_count = 0
    skip_reasons = {}
    processed_cases = []
    error_summary = []

    for index, row in df.iterrows():
        try:
            if indices and index not in indices:
                continue

            # Convert aisummary to string early to handle potential float/NaN values safely
            aisummary_value = row["aisummary"]
            if aisummary_value is None or (isinstance(aisummary_value, float) and pd.isna(aisummary_value)):
                reason = "null_or_nan"
                skip_reasons[reason] = skip_reasons.get(reason, 0) + 1
                log_message(f"⚠️ Skipping case {index}: aisummary is None or NaN", level='DEBUG')
                skipped_count += 1
                continue

            summary_str = str(aisummary_value).strip()

            # Skip if empty or too short
            if not summary_str or len(summary_str) <= 10:
                reason = "too_short_or_empty"
                skip_reasons[reason] = skip_reasons.get(reason, 0) + 1
                log_message(f"⚠️ Skipping case {index}: aisummary too short or empty (length: {len(summary_str)})", level='DEBUG')
                skipped_count += 1
                continue

            # Skip if already contains JSON (already translated)
            if "{" in summary_str:
                reason = "already_translated"
                skip_reasons[reason] = skip_reasons.get(reason, 0) + 1
                log_message(f"⚠️ Skipping case {index}: aisummary already contains JSON (likely already translated)", level='DEBUG')
                skipped_count += 1
                continue

            # Process translation
            log_message(f"🔄 Processing translation for case {index}", level='DEBUG')
            text = summary_str
            prompt = f'I will give you the overview of a legal case in English. I want you to provide me with the Chinese translation of the same case overview. You return your answer as a JSON that looks like this: {{"English": "The case overview in English", "Chinese": "The Chinese translation of the same case overview"}}.\n The English version of the Legal Case Overview is: "{text}"'

            response = await vertex_genai_text_async(prompt, model_name=Constants.SMART_MODEL_FREE)
            translated_result = clean_ai_summary_translations_response(response)
            df.loc[index, "aisummary"] = translated_result

            processed_cases.append({
                "case_index": index,
                "original_length": len(summary_str),
                "translated": True
            })
            log_message(f"✅ Successfully translated case {index}", level='DEBUG')
            processed_count += 1

        except Exception as e:
            error_count += 1
            error_detail = {
                "case_index": index,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "aisummary_type": type(row["aisummary"]).__name__ if "aisummary" in row else "unknown"
            }
            error_summary.append(error_detail)
            log_message(f"❌ Error processing case {index}: {str(e)} (type: {type(e).__name__})", level='ERROR')
            # Continue processing other cases instead of failing completely
            continue

    # Log output results
    langfuse.get_client().update_current_span(
        output={
            "processed_count": processed_count,
            "skipped_count": skipped_count,
            "error_count": error_count,
            "skip_reasons": skip_reasons,
            "processed_cases": processed_cases[:10],  # Limit to first 10 for brevity
            "error_types": [e['error_type'] for e in error_summary] if error_summary else []
        }
    )

    log_message(f"✅ Summary Translation completed. Processed: {processed_count}, Skipped: {skipped_count}, Errors: {error_count}", level='INFO')
    if skip_reasons:
        log_message(f"📊 Skip reasons breakdown: {skip_reasons}", level='INFO')
    if error_summary:
        log_message(f"📊 Error types: {[e['error_type'] for e in error_summary]}", level='INFO')


def clean_ai_summary_translations_response(response):
    if response:
        try:
            # Look for content between curly braces
            json_match = re.search(r'\{[^{}]*\}', response)
            if json_match:
                json_str = json_match.group(0)
                # Verify it has both English and Chinese keys
                if all(key in json_str for key in ["English", "Chinese"]):
                    return json_str
            
            # If no valid JSON found or missing required keys
            return '{"English":"Unknown", "Chinese": "未知"}'
        
        except Exception as e:
            print(f"Error parsing response: {e}")
            return '{"English":"Unknown", "Chinese": "未知"}'
    else:
        return '{"English":"Unknown", "Chinese": "未知"}'
    
@observe()
async def _translate_batch_of_steps(batch_steps_dict: dict) -> Optional[dict]:
    """
    Translates a single batch of steps. If translation or validation fails,
    it recursively tries with a smaller batch size for that specific batch.
    """
    if not batch_steps_dict:
        return {}
    json_str_to_translate = json.dumps(batch_steps_dict, ensure_ascii=False)
    prompt = (
        "Translate the proceeding text in this legal case JSON from English to Chinese. "
        "Maintain the same JSON structure with step numbers as keys. "
        f"JSON to translate: {json_str_to_translate}"
    )

    current_processing_size = len(batch_steps_dict)

    try:
        response = await vertex_genai_text_async(prompt, model_name=Constants.SMART_MODEL_FREE)
        translated_batch_json = extract_translation_json_from_text(response)
        original_keys = set(batch_steps_dict.keys())
        translated_keys = set(translated_batch_json.keys())
        missing_keys = original_keys - translated_keys
        extra_keys = translated_keys - original_keys

        if missing_keys or extra_keys: # If there are missing or extra keys, attempt to split and retry
            langfuse.get_client().update_current_span(metadata={"missing_keys": list(missing_keys), "extra_keys": list(extra_keys)})
            if missing_keys:
                log_message(f"Missing steps in translation for batch of size {current_processing_size}: {missing_keys}.", level="WARNING")
            if extra_keys:
                log_message(f"Extra steps found in translation for batch of size {current_processing_size}: {extra_keys}.", level="ERROR")

            if current_processing_size > 1:
                log_message(f"Retrying failed batch (size {current_processing_size}) by splitting.", level="INFO")
                items = list(batch_steps_dict.items())
                mid_point = current_processing_size // 2
                first_half_dict = dict(items[:mid_point])
                second_half_dict = dict(items[mid_point:])

                translated_first_half = await _translate_batch_of_steps(first_half_dict)
                translated_second_half = await _translate_batch_of_steps(second_half_dict)

                if translated_first_half is not None and translated_second_half is not None:
                    merged_results = {}
                    merged_results.update(translated_first_half)
                    merged_results.update(translated_second_half)
                    return merged_results
                else:
                    log_message(f"Failed to translate one or both halves of the split batch (original size {current_processing_size}).", level="ERROR")
                    return None
            else: # current_processing_size is 1 and still has issues
                log_message(f"Cannot translate batch of size 1 due to missing/extra keys. Original: {original_keys}, Translated: {translated_keys}", level="ERROR")
                return None

        return translated_batch_json
    except Exception as e:
        log_message(f"Error during _translate_batch_of_steps (batch size {current_processing_size}): {e}", level="ERROR")
        if current_processing_size > 1:
            log_message(f"Retrying failed batch (size {current_processing_size}) due to exception by splitting.", level="INFO")
            items = list(batch_steps_dict.items())
            mid_point = current_processing_size // 2
            first_half_dict = dict(items[:mid_point])
            second_half_dict = dict(items[mid_point:])

            translated_first_half = await _translate_batch_of_steps(first_half_dict)
            translated_second_half = await _translate_batch_of_steps(second_half_dict)

            if translated_first_half is not None and translated_second_half is not None:
                merged_results = {}
                merged_results.update(translated_first_half)
                merged_results.update(translated_second_half)
                return merged_results
            else:
                log_message(f"Failed to translate one or both halves of the split batch (original size {current_processing_size}) after exception.", level="ERROR")
                return None
        return None

@observe()
async def translate_steps(cases_steps_df: pd.DataFrame) -> pd.DataFrame:
    unique_case_ids = cases_steps_df["case_id"].unique()
    cases_steps_with_translation = pd.DataFrame()
    tasks = []
    semaphore = asyncio.Semaphore(10)
    
    # Create tasks with case_id tracking
    for case_id in unique_case_ids:
        # Pass case_id as a parameter to coroutine
        tasks.append(Constants.sem_task(semaphore, translate_steps_for_a_case(cases_steps_df, case_id, batch_size=Constants.TRANSLATION_BATCH_SIZE)))
    

    # Get results in order with original case_ids
    translated_steps = await asyncio.gather(*tasks)
    
    # Pair results with original case_ids using zip
    for result, case_id in zip(translated_steps, unique_case_ids):
        if result is not None:
            cases_steps_with_translation = pd.concat([cases_steps_with_translation, result])
        else:
            # Use the case_id from our original list
            original_steps = cases_steps_df[cases_steps_df["case_id"] == case_id]
            cases_steps_with_translation = pd.concat([cases_steps_with_translation, original_steps])
    
    return cases_steps_with_translation


@observe()
async def translate_steps_for_a_case(cases_steps_df: pd.DataFrame, case_id: int, batch_size: int = Constants.TRANSLATION_BATCH_SIZE) -> Optional[pd.DataFrame]:
    """
    Translates case proceeding steps from English to Chinese and returns a structured JSON
    Steps are naturally sorted (e.g., 3.1 comes before 3.10)
    """
    
    log_message(f"🧠 Starting Steps Translation for case ID: {case_id}")
    
    case_steps = cases_steps_df[cases_steps_df["case_id"] == case_id]
    if "proceeding_text_cn" in case_steps.columns and case_steps["proceeding_text_cn"].notna().all():
        return case_steps
    
    if case_steps.empty:
        log_message(f"No steps found for case ID: {case_id}")
        return None
    
    # Build steps dictionary
    steps_dict = {}
    for _, row in case_steps.iterrows():
        step_nb = str(row['step_nb'])  # Convert to string to ensure valid JSON key
        proceeding_text = str(row['proceeding_text']).strip()
        if proceeding_text:  # Only include non-empty proceedings
            steps_dict[step_nb] = proceeding_text
    
    if not steps_dict:
        print("No valid proceeding texts found")
        return case_steps
    
    # Create sorted dictionary and convert to JSON
    sorted_steps = dict(sorted(steps_dict.items(), key=lambda x: natural_sort_key(x[0])))

    all_translated_steps_json = {}
    items = list(sorted_steps.items())
    
    for i in range(0, len(items), batch_size):
        current_batch_dict = dict(items[i:i + batch_size])
        log_message(f"Translating batch for case {case_id}, steps {list(current_batch_dict.keys())[0]} to {list(current_batch_dict.keys())[-1]}", level="DEBUG")
        
        translated_sub_batch_json = await _translate_batch_of_steps(current_batch_dict)
        
        if translated_sub_batch_json is not None:
            all_translated_steps_json.update(translated_sub_batch_json)
        else:
            log_message(f"Failed to translate batch starting with step {list(current_batch_dict.keys())[0]} for case {case_id}. These steps will not be updated.", level="ERROR")
            # If a batch fails entirely, we might decide to return the original case_steps
            # or only update with successfully translated batches.
            # For now, we'll continue and update with what we have.
    
    # Update the DataFrame with translations
    for step_nb, chinese_text in all_translated_steps_json.items():
        mask = (case_steps["case_id"] == case_id) & (case_steps["step_nb"].astype(str) == step_nb)
        case_steps.loc[mask, "proceeding_text_cn"] = chinese_text
    
    log_message(f"✅  Successfully updated translations Steps for case ID: {case_id}")
    return case_steps
            


def natural_sort_key(step_nb):
    """
    Convert step number (like '3.10') to a tuple of integers for natural sorting
    Example: '3.10' -> (3, 10), '3' -> (3, 0)
    """
    parts = str(step_nb).split('.')
    return tuple(int(part) for part in parts) + (0,) * (2 - len(parts))


def extract_translation_json_from_text(text):
    """Helper function to extract valid JSON from text"""
    try:
        # First try to find the outermost pair of curly braces
        start = text.find('{')
        end = text.rfind('}')
        if start != -1 and end == -1: 
            print(f"Warning: No closing brace found for JSON, going to add one")
           
            matches = list(re.finditer(r'"\d+\.\d{2}"', text))
            if matches:
                last_match = matches[-1]
                text = text[:last_match.start()]  # find the last step number
                text = text[:text.rfind('"')+1] + '}' # add the closing brace after the end of the last step text
                end = text.rfind('}')
            else:
                print(f"Warning: No step number found in the text")
                return {}
        if start != -1 and end != -1:
            json_str = text[start:end+1]
            return json.loads(json_str)
    except json.JSONDecodeError:
        print("Failed to parse JSON from response")
    return {}


if __name__ == "__main__":
    asyncio.run(get_all_ai_summary_translations())

    # cases_df = get_table_from_GZ("tb_case", force_refresh=True)
    # cases_df = cases_df[cases_df["id"] > 10000]
    # cases_steps_df = get_table_from_GZ("tb_case_steps", force_refresh=True)
    # for index, row in cases_df.iloc[::-1].iterrows():
    #     cases_steps = translate_steps_for_a_case(cases_steps_df, row["id"])
    #     if cases_steps is not None:
    #         insert_and_update_df_to_GZ_batch(cases_steps, "tb_case_steps", "id")
